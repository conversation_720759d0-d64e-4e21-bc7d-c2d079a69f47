{"name": "study-flow-capture", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite dev", "build": "svelte-kit sync && vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "sync": "svelte-kit sync"}, "devDependencies": {"@sveltejs/adapter-auto": "^3.2.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@tsconfig/svelte": "^5.0.4", "@types/node": "^22.15.14", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "svelte": "^5.28.1", "svelte-check": "^4.1.6", "typescript": "~5.8.3", "vite": "^6.3.5"}, "dependencies": {"@popperjs/core": "^2.11.8", "bootstrap": "^5.3.6", "clsx": "^2.1.1"}}