// test-db.js - Simple script to test database functionality
import Database from 'better-sqlite3';
import fs from 'fs';

const DB_PATH = './survey-results.sqlite';

// Check if database file exists
if (fs.existsSync(DB_PATH)) {
  console.log('✅ Database file exists');
  
  const db = new Database(DB_PATH);
  
  // Check tables
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
  console.log('📋 Tables in database:', tables.map(t => t.name));
  
  // Check survey_results table structure
  const columns = db.prepare("PRAGMA table_info(survey_results)").all();
  console.log('🏗️  survey_results table structure:');
  columns.forEach(col => {
    console.log(`  - ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.pk ? 'PRIMARY KEY' : ''}`);
  });
  
  // Check if there are any records
  const count = db.prepare("SELECT COUNT(*) as count FROM survey_results").get();
  console.log(`📊 Number of survey records: ${count.count}`);
  
  // Show recent records if any
  if (count.count > 0) {
    const recent = db.prepare("SELECT * FROM survey_results ORDER BY timestamp DESC LIMIT 3").all();
    console.log('🔍 Recent records:');
    recent.forEach((record, index) => {
      console.log(`  Record ${index + 1}:`);
      console.log(`    ID: ${record.id}`);
      console.log(`    Respondent: ${record.respondent_id}`);
      console.log(`    Age: ${record.age}`);
      console.log(`    Gender: ${record.gender}`);
      console.log(`    Education: ${record.education_level}`);
      console.log(`    Interest: ${record.interest_in_topic}`);
      console.log(`    Pre-test: env=${record.pre_env}, econ=${record.pre_econ}, health=${record.pre_health}`);
      console.log(`    Post-test: q1=${record.post_q1}, q2=${record.post_q2}, q3=${record.post_q3}`);
      console.log(`    Timestamp: ${record.timestamp}`);
      console.log('');
    });
  }
  
  db.close();
} else {
  console.log('❌ Database file does not exist yet');
  console.log('💡 The database will be created when the first survey is submitted');
}
