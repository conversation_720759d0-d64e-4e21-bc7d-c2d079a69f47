<script lang="ts">
  import { page } from '$app/stores';
</script>

<div class="container min-vh-100 d-flex flex-column justify-content-center align-items-center p-4">
  <div class="text-center">
    <h1 class="display-1 fw-bold mb-3" style="color: var(--study-primary);">
      {$page.status || 404}
    </h1>
    <h2 class="fs-4 mb-4">
      {$page.error?.message || "Page non trouvée"}
    </h2>
    <p class="mb-4">
      La page que vous recherchez n'existe pas ou une erreur s'est produite.
    </p>
    <a href="/" class="btn btn-primary">
      Retour à l'accueil
    </a>
  </div>
</div>
