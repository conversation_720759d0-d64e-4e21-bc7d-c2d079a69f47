<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { goto } from '$app/navigation';
  import StudyProgress from '../../components/StudyProgress.svelte';

  const studyTexts = [
    `Texte A: L'intelligence artificielle transformera notre société de manière profonde et principalement bénéfique. \
    Les avancées en matière d'IA permettent déjà de résoudre des problèmes complexes dans des domaines comme la médecine, \
    l'éducation et l'environnement. Des études récentes montrent que l'IA pourrait augmenter la productivité mondiale \
    de 40% d'ici 2035, tout en créant de nouveaux emplois et en améliorant notre qualité de vie.`,
    `Texte B: La priorité donnée aux préoccupations environnementales génère des bénéfices économiques souvent négligés. \
    Selon plusieurs études économiques récentes, les investissements dans les technologies vertes créent trois fois plus \
    d'emplois que les industries fossiles traditionnelles. De plus, le coût de l'inaction face au changement climatique \
    pourrait réduire le PIB mondial de 18% d'ici 2050, bien plus que les coûts de transition estimés.`,
    `Texte C: Les réseaux sociaux ont considérablement transformé la façon dont nous communiquons et partageons l'information. \
    Des recherches récentes montrent qu'ils ont facilité l'accès à une diversité d'opinions sans précédent et permis à des \
    voix traditionnellement marginalisées de se faire entendre. Une étude de 2022 révèle que 67% des utilisateurs affirment \
    avoir été exposés à des points de vue différents des leurs grâce aux médias sociaux.`,
    `Texte D: L'intelligence artificielle présente des risques majeurs qui sont souvent minimisés. Des experts en éthique \
    et en sécurité informatique alertent sur les dangers de la dépendance excessive aux systèmes automatisés. Une étude \
    récente montre que 76% des applications d'IA amplifient les biais sociaux existants. De plus, l'automatisation pourrait \
    éliminer jusqu'à 800 millions d'emplois d'ici 2030 sans garantie de création équivalente.`,
    `Texte E: Les réseaux sociaux ont un impact globalement négatif sur la qualité du débat public. Des recherches en \
    sciences cognitives montrent qu'ils favorisent la polarisation des opinions et la diffusion rapide de fausses informations. \
    Une étude du MIT révèle que les fausses nouvelles se propagent six fois plus vite que les informations vérifiées sur \
    Twitter, et que 70% des utilisateurs ne vérifient pas les sources avant de partager du contenu.`
  ];
  
  let selectedTextIndex: number | null = null;
  let hasRead = false;
  let minReadTime = 10; // seconds
  let timer: ReturnType<typeof setInterval>;
  let remainingTime = minReadTime;
  
  $: canContinue = hasRead && selectedTextIndex !== null;

  function selectText(index: number) {
    selectedTextIndex = index;
    if (!hasRead) {
      remainingTime = minReadTime;
      clearInterval(timer);
      timer = setInterval(() => {
        remainingTime--;
        if (remainingTime <= 0) {
          hasRead = true;
          clearInterval(timer);
        }
      }, 1000);
    }
  }

  function handleContinue() {
    if (canContinue) {
      goto('/post-test');
    }
  }

  onDestroy(() => {
    clearInterval(timer);
  });
</script>

<div class="min-vh-100 bg-light py-4 px-3">
  <div class="container py-4">
    <StudyProgress currentStep={3} totalSteps={6} />
    
    <h1 class="display-5 fw-bold mb-4 text-center">Lecture d'article</h1>
    
    <div class="mb-5">
      <p class="mb-4">
        Veuillez choisir et lire attentivement l'un des textes suivants. Vous devrez lire pendant au moins {minReadTime} secondes 
        avant de pouvoir continuer.
      </p>
      
      <div class="row mb-4">
        {#each studyTexts as text, i}
          <div class="col-md-6 mb-3">
            <div class="card h-100" class:border-primary={selectedTextIndex === i} style="cursor: pointer;">
              <div 
                class="card-body" 
                role="button"
                tabindex="0"
                on:click={() => selectText(i)}
                on:keydown={(e) => e.key === 'Enter' && selectText(i)}
                aria-pressed={selectedTextIndex === i}
              >
                <h5 class="card-title mb-3">Texte {String.fromCharCode(65 + i)}</h5>
                <p class="card-text small">
                  {text.substring(0, 100)}...
                </p>
                <div class="text-center mt-3">
                  <button
                    class="btn btn-sm"
                    class:btn-outline-primary={selectedTextIndex !== i}
                    class:btn-primary={selectedTextIndex === i}
                    on:click|stopPropagation={() => selectText(i)}
                  >
                    {selectedTextIndex === i ? 'Sélectionné' : 'Sélectionner'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        {/each}
      </div>
      
      {#if selectedTextIndex !== null}
        <div class="card mb-4">
          <div class="card-body">
            <h3 class="card-title mb-3">Texte {String.fromCharCode(65 + selectedTextIndex)}</h3>
            <p class="card-text">
              {studyTexts[selectedTextIndex]}
            </p>
            {#if !hasRead}
              <div class="alert alert-info mt-3">
                Veuillez lire attentivement pendant encore {remainingTime} secondes...
              </div>
            {/if}
          </div>
        </div>
      {/if}
      
      <div class="text-center">
        <button
          on:click={handleContinue}
          disabled={!canContinue}
          class="btn btn-primary btn-lg"
        >
          Continuer
        </button>
      </div>
    </div>
  </div>
</div>
