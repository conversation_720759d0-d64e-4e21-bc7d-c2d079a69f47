<script lang="ts">
  import { onDestroy } from 'svelte';
  import { goto } from '$app/navigation';
  import StudyProgress from '../../components/StudyProgress.svelte';
  import LikertScale from '../../components/LikertScale.svelte';

  let preTestAnswers: Record<string, number> = {};
  let isComplete = false;

  function handleAnswer(question: string, value: number) {
    console.log('handleAnswer called', question, value);
    preTestAnswers = { ...preTestAnswers, [question]: value };
    isComplete = Object.keys(preTestAnswers).length === 3 &&
      Object.values(preTestAnswers).every(v => typeof v === 'number');
  }

  function handleContinue() {
    if (isComplete) {
      goto('/text-exposure');
    }
  }
</script>

<div class="min-vh-100 bg-light py-4 px-3">
  <div class="container py-4">
    <StudyProgress currentStep={2} totalSteps={6} />
    
    <h1 class="display-5 fw-bold mb-4 text-center">Questionnaire initial</h1>
    
    <div class="mb-5">
      <p class="mb-4">
        Veuillez indiquer votre degré d'accord avec les affirmations suivantes.
      </p>
      
      <div class="card mb-4">
        <div class="card-body">
          <LikertScale
            question="La protection de l'environnement devrait être prioritaire même si cela ralentit la croissance économique."
            value={preTestAnswers['env'] || null}
            onChange={(value) => handleAnswer('env', value)}
          />
        </div>
      </div>
      
      <div class="card mb-4">
        <div class="card-body">
          <LikertScale
            question="Les inégalités économiques sont un problème majeur qui nécessite l'intervention de l'État."
            value={preTestAnswers['econ'] || null}
            onChange={(value) => handleAnswer('econ', value)}
          />
        </div>
      </div>
      
      <div class="card mb-5">
        <div class="card-body">
          <LikertScale
            question="Le système de santé devrait être accessible à tous, quels que soient les moyens financiers."
            value={preTestAnswers['health'] || null}
            onChange={(value) => handleAnswer('health', value)}
          />
        </div>
      </div>
      
      <div class="text-center">
        <button
          on:click={handleContinue}
          disabled={!isComplete}
          class="btn btn-primary btn-lg"
        >
          Continuer
        </button>
      </div>
    </div>
  </div>
</div>
