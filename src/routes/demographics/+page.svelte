<script lang="ts">
  import { onDestroy } from 'svelte';
  import { goto } from '$app/navigation';
  import StudyProgress from '../../components/StudyProgress.svelte';
  import LikertScale from '../../components/LikertScale.svelte';
  import { surveyStore, updateDemographics, submitSurvey } from '../../store/survey';

  let demographics: Record<string, any> = {
    age: null,
    gender: '',
    educationLevel: '',
    interestInTopic: null
  };

  let isSubmitting = false;

  // Subscribe to store to get existing data
  const unsubscribe = surveyStore.subscribe(data => {
    demographics = {
      age: data.age,
      gender: data.gender,
      educationLevel: data.educationLevel,
      interestInTopic: data.interestInTopic
    };
  });

  onDestroy(() => {
    unsubscribe();
  });

  function handleAge(e: Event) {
    const val = parseInt((e.target as HTMLInputElement).value) || null;
    demographics.age = val;
    updateDemographics({ age: val });
  }

  function handleGender(e: Event) {
    const val = (e.target as HTMLSelectElement).value;
    demographics.gender = val;
    updateDemographics({ gender: val });
  }

  function handleEducation(e: Event) {
    const val = (e.target as HTMLSelectElement).value;
    demographics.educationLevel = val;
    updateDemographics({ educationLevel: val });
  }

  function handleInterest(val: number) {
    demographics.interestInTopic = val;
    updateDemographics({ interestInTopic: val });
  }

  function isFormValid() {
    return (
      demographics.age !== null &&
      demographics.age >= 18 &&
      demographics.age <= 100 &&
      demographics.gender !== '' &&
      demographics.educationLevel !== '' &&
      demographics.interestInTopic !== null
    );
  }

  async function handleSubmit() {
    if (!isFormValid()) {
      alert("Veuillez remplir tous les champs obligatoires avant de continuer.");
      return;
    }

    isSubmitting = true;

    try {
      const result = await submitSurvey();

      if (result.success) {
        goto('/thank-you');
      } else {
        alert(`Erreur lors de la soumission: ${result.error || 'Erreur inconnue'}`);
      }
    } catch (error) {
      console.error('Submission error:', error);
      alert('Erreur lors de la soumission. Veuillez réessayer.');
    } finally {
      isSubmitting = false;
    }
  }
</script>

<div class="min-vh-100 bg-light py-4 px-3">
  <div class="container py-4">
    <StudyProgress currentStep={5} totalSteps={6} />
    <h1 class="display-5 fw-bold mb-4 text-center">Informations démographiques</h1>

    <div class="card card-body mb-4">
      <p class="card-text mb-4">
        Pour terminer l'étude, merci de nous fournir quelques informations démographiques.
        Ces données sont anonymes et serviront uniquement à des fins statistiques.
      </p>

      <form class="needs-validation" novalidate>
        <div class="mb-4">
          <label for="age" class="form-label fw-medium">Âge</label>
          <input
            type="number"
            class="form-control"
            id="age"
            placeholder="Votre âge"
            min="18"
            max="100"
            value={demographics.age || ''}
            on:change={handleAge}
            required
          />
        </div>

        <div class="mb-4">
          <label for="gender" class="form-label fw-medium">Genre</label>
          <select
            class="form-select"
            id="gender"
            value={demographics.gender}
            on:change={handleGender}
            required
          >
            <option value="" disabled selected>Sélectionnez une option</option>
            <option value="homme">Homme</option>
            <option value="femme">Femme</option>
            <option value="non-binaire">Non-binaire</option>
            <option value="autre">Autre</option>
            <option value="prefere-ne-pas-repondre">Je préfère ne pas répondre</option>
          </select>
        </div>

        <div class="mb-4">
          <label for="education" class="form-label fw-medium">Niveau d'éducation</label>
          <select
            class="form-select"
            id="education"
            value={demographics.educationLevel}
            on:change={handleEducation}
            required
          >
            <option value="" disabled selected>Sélectionnez une option</option>
            <option value="primaire">École primaire</option>
            <option value="secondaire">École secondaire / Lycée</option>
            <option value="bac">Baccalauréat</option>
            <option value="licence">Licence / Bachelor</option>
            <option value="master">Master</option>
            <option value="doctorat">Doctorat</option>
            <option value="autre">Autre</option>
          </select>
        </div>

        <div class="mb-4">
          <label for="interest-scale" id="interest-label" class="form-label fw-medium">À quel point êtes-vous intéressé(e) par les questions de vie privée en ligne ?</label>
          <LikertScale
            id="interest-scale"
            question=""
            labels={['Pas du tout intéressé(e)', 'Peu intéressé(e)', 'Moyennement intéressé(e)', 'Très intéressé(e)', 'Extrêmement intéressé(e)']}
            onChange={handleInterest}
            value={demographics.interestInTopic}
          />
        </div>
      </form>
    </div>

    <div class="text-center mt-4">
      <button
        on:click={handleSubmit}
        disabled={!isFormValid() || isSubmitting}
        class="btn btn-primary px-4 py-2"
      >
        {#if isSubmitting}
          <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
          Envoi en cours...
        {:else}
          Terminer l'étude
        {/if}
      </button>
    </div>
  </div>
</div>
