<script lang="ts">
  import { onDestroy } from 'svelte';
  import { goto } from '$app/navigation';
  import StudyProgress from '../../components/StudyProgress.svelte';
  import LikertScale from '../../components/LikertScale.svelte';
  import { surveyStore, updatePostTest } from '../../store/survey';

  let postTestAnswers: Record<string, number> = {};
  let isComplete = false;

  // Subscribe to store to get existing data
  const unsubscribe = surveyStore.subscribe(data => {
    postTestAnswers = {
      q1: data.postTest.q1 || 0,
      q2: data.postTest.q2 || 0,
      q3: data.postTest.q3 || 0
    };
    // Remove zero values for display
    Object.keys(postTestAnswers).forEach(key => {
      if (postTestAnswers[key] === 0) {
        delete postTestAnswers[key];
      }
    });

    isComplete = Object.keys(postTestAnswers).length === 3 &&
      Object.values(postTestAnswers).every(v => typeof v === 'number' && v > 0);
  });

  onDestroy(() => {
    unsubscribe();
  });

  function handleAnswer(question: string, value: number) {
    postTestAnswers = { ...postTestAnswers, [question]: value };

    // Update the store
    updatePostTest({ [question]: value });

    isComplete = Object.keys(postTestAnswers).length === 3 &&
      Object.values(postTestAnswers).every(v => typeof v === 'number' && v > 0);
  }

  function handleContinue() {
    if (isComplete) {
      goto('/demographics');
    } else {
      alert('Veuillez répondre à toutes les questions avant de continuer.');
    }
  }
</script>

<div class="container-fluid bg-light min-vh-100 py-4">
  <div class="container py-4">
    <StudyProgress currentStep={4} totalSteps={6} />
    <h1 class="display-5 fw-bold mb-4 text-center">Après lecture - Vos opinions</h1>
    <div class="card card-body mb-4">
      <p class="card-text mb-4">
        Maintenant que vous avez lu le texte, veuillez indiquer votre degré d'accord avec les déclarations suivantes :
      </p>

      <div class="mb-4">
        <p class="fw-medium mb-2">1. La régulation gouvernementale des entreprises technologiques est nécessaire pour protéger la vie privée des citoyens.</p>
        <LikertScale
          id="q1"
          question="1. La régulation gouvernementale des entreprises technologiques est nécessaire pour protéger la vie privée des citoyens."
          labels={['Pas du tout d\'accord', 'Pas d\'accord', 'Neutre', 'D\'accord', 'Tout à fait d\'accord']}
          onChange={(val) => handleAnswer('q1', val)}
          value={postTestAnswers['q1']}
        />
      </div>

      <div class="mb-4">
        <p class="fw-medium mb-2">2. Les grandes entreprises technologiques s'autorégulent efficacement et n'ont pas besoin de supervision supplémentaire.</p>
        <LikertScale
          id="q2"
          question="2. Les grandes entreprises technologiques s'autorégulent efficacement et n'ont pas besoin de supervision supplémentaire."
          labels={['Pas du tout d\'accord', 'Pas d\'accord', 'Neutre', 'D\'accord', 'Tout à fait d\'accord']}
          onChange={(val) => handleAnswer('q2', val)}
          value={postTestAnswers['q2']}
        />
      </div>

      <div class="mb-4">
        <p class="fw-medium mb-2">3. Le gouvernement devrait imposer des limites plus strictes sur la manière dont les entreprises technologiques collectent et utilisent les données personnelles.</p>
        <LikertScale
          id="q3"
          question="3. Le gouvernement devrait imposer des limites plus strictes sur la manière dont les entreprises technologiques collectent et utilisent les données personnelles."
          labels={['Pas du tout d\'accord', 'Pas d\'accord', 'Neutre', 'D\'accord', 'Tout à fait d\'accord']}
          onChange={(val) => handleAnswer('q3', val)}
          value={postTestAnswers['q3']}
        />
      </div>
    </div>

    <div class="d-flex justify-content-center mt-4">
      <button
        on:click={handleContinue}
        disabled={!isComplete}
        class="btn btn-primary px-4 py-2">
        Continuer
      </button>
    </div>
  </div>
</div>
