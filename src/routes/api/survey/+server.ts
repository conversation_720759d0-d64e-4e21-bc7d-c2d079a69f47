// src/routes/api/survey/+server.ts
import type { RequestHand<PERSON> } from '@sveltejs/kit';
import db from '$lib/db-init';

function isInteger(val: unknown): val is number {
  return typeof val === 'number' && Number.isInteger(val);
}

function sanitizeString(val: unknown): string {
  return typeof val === 'string' ? val.trim() : '';
}

export const POST: RequestHandler = async ({ request }) => {
  try {
    const data = await request.json();

    // Basic validation and sanitization
    const respondent_id = sanitizeString(data.respondent_id) || `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const age = isInteger(data.age) ? data.age : null;
    const gender = sanitizeString(data.gender);
    const education_level = sanitizeString(data.education_level);
    const interest_in_topic = isInteger(data.interest_in_topic) ? data.interest_in_topic : null;

    // Pre-test answers (environment, economics, health)
    const pre_env = isInteger(data.pre_env) ? data.pre_env : null;
    const pre_econ = isInteger(data.pre_econ) ? data.pre_econ : null;
    const pre_health = isInteger(data.pre_health) ? data.pre_health : null;

    // Post-test answers (tech regulation questions)
    const post_q1 = isInteger(data.post_q1) ? data.post_q1 : null;
    const post_q2 = isInteger(data.post_q2) ? data.post_q2 : null;
    const post_q3 = isInteger(data.post_q3) ? data.post_q3 : null;

    // Optionally store all raw answers as JSON
    const raw_answers_json = JSON.stringify(data);

    // Insert into DB
    const stmt = db.prepare(`
      INSERT INTO survey_results (
        respondent_id, age, gender, education_level, interest_in_topic,
        pre_env, pre_econ, pre_health,
        post_q1, post_q2, post_q3,
        raw_answers_json
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      respondent_id,
      age,
      gender,
      education_level,
      interest_in_topic,
      pre_env,
      pre_econ,
      pre_health,
      post_q1,
      post_q2,
      post_q3,
      raw_answers_json
    );

    return new Response(JSON.stringify({ success: true, respondent_id }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (err) {
    console.error('Survey submission error:', err);
    return new Response(
      JSON.stringify({ success: false, error: (err as Error).message }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    );
  }
};
