// src/routes/api/survey/+server.ts
import type { Request<PERSON>and<PERSON> } from '@sveltejs/kit';
import db from '$lib/db-init';

function isInteger(val: unknown): val is number {
  return typeof val === 'number' && Number.isInteger(val);
}

function sanitizeString(val: unknown): string {
  return typeof val === 'string' ? val.trim() : '';
}

export const POST: RequestHandler = async ({ request }) => {
  try {
    const data = await request.json();

    // Basic validation and sanitization
    const respondent_id = sanitizeString(data.respondent_id);
    const age = isInteger(data.age) ? data.age : null;
    const gender = sanitizeString(data.gender);
    const education_level = sanitizeString(data.education_level);

    const pre_q1 = isInteger(data.pre_q1) ? data.pre_q1 : null;
    const pre_q2 = isInteger(data.pre_q2) ? data.pre_q2 : null;
    const pre_q3 = isInteger(data.pre_q3) ? data.pre_q3 : null;
    const post_q1 = isInteger(data.post_q1) ? data.post_q1 : null;
    const post_q2 = isInteger(data.post_q2) ? data.post_q2 : null;
    const post_q3 = isInteger(data.post_q3) ? data.post_q3 : null;

    // Optionally store all raw answers as JSON
    const raw_answers_json = JSON.stringify(data);

    // Insert into DB
    const stmt = db.prepare(`
      INSERT INTO survey_results (
        respondent_id, age, gender, education_level,
        pre_q1, pre_q2, pre_q3,
        post_q1, post_q2, post_q3,
        raw_answers_json
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      respondent_id,
      age,
      gender,
      education_level,
      pre_q1,
      pre_q2,
      pre_q3,
      post_q1,
      post_q2,
      post_q3,
      raw_answers_json
    );

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (err) {
    return new Response(
      JSON.stringify({ success: false, error: (err as Error).message }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    );
  }
};