<script lang="ts">
  export let currentStep: number
  export let totalSteps: number
  export let className: string = ''
  let progress = 0
  $: progress = totalSteps > 0 ? Math.round((currentStep / totalSteps) * 100) : 0
</script>

<div class={className + ' mb-4'}>
  <div class="d-flex justify-content-between mb-2 small text-muted">
    <span>Étape {currentStep} sur {totalSteps}</span>
    <span>{progress}%</span>
  </div>
  <div class="progress" style="height: 0.5rem;">
    <div
      class="progress-bar"
      role="progressbar"
      style="width: {progress}%; background-color: var(--study-primary);"
      aria-valuenow={progress}
      aria-valuemin={0}
      aria-valuemax={100}
    ></div>
  </div>
</div>