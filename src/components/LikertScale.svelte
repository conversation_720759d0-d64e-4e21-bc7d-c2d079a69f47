<script lang="ts">
  export let question: string
  export let value: number | null
  export let onChange: (value: number) => void
  export let labels: string[] = [
    "Pas du tout d'accord",
    "Plutôt pas d'accord",
    "Neutre",
    "Plutôt d'accord",
    "Tout à fait d'accord"
  ]
  export let id: string = ''
  
  // Internal value for the slider; null until user interacts
  let internalValue: number | null = null
  
  // Track if the user has interacted with the scale
  let hasInteracted = false
  
  // When external value changes (from store), update internal value
  $: if (value !== null && value !== undefined && value !== internalValue) {
    internalValue = value
  }
  // Helper to get the closest Likert label for a float value
  function getLikertLabel(val: number) {
    const idx = Math.round(val) - 1;
    if (idx >= 0 && idx < labels.length) {
      return labels[idx];
    }
    return "";
  }
</script>

<div class="mb-4">
  {#if question}
    <p class="mb-3 fw-medium">{question}</p>
  {/if}
  <input
    type="range"
    min="1"
    max="5"
    step="0.01"
    class="form-range mb-3"
    id={id || 'likert-scale'}
    value={internalValue !== null ? internalValue : 3}
    aria-valuenow={internalValue !== null ? +internalValue.toFixed(2) : 3}
    aria-valuetext={
      internalValue !== null
        ? `${internalValue.toFixed(2)} (${getLikertLabel(internalValue)})`
        : `3.00 (${labels[2]})`
    }
    aria-valuemin="1"
    aria-valuemax="5"
    on:input={(e) => {
      const target = e.target as HTMLInputElement;
      const v = parseFloat(target.value);
      if (internalValue === null || !hasInteracted) {
        internalValue = v;
        hasInteracted = true;
        onChange(v);
      } else if (internalValue !== v) {
        internalValue = v;
        hasInteracted = true;
        onChange(v);
      }
    }}
    on:change={(e) => {
      const target = e.target as HTMLInputElement;
      const v = parseFloat(target.value);
      if (internalValue === null || !hasInteracted) {
        internalValue = v;
        hasInteracted = true;
        onChange(v);
      } else if (internalValue !== v) {
        internalValue = v;
        hasInteracted = true;
        onChange(v);
      }
    }}
    on:click={() => {
      // Log and handle explicit click, even if value does not change
      console.log('Likert clicked: internalValue', internalValue, 'hasInteracted', hasInteracted);
      if (internalValue === null || !hasInteracted) {
        internalValue = 3;
        hasInteracted = true;
        onChange(3);
      }
    }}
  />
  <div class="d-flex justify-content-between small text-muted mb-2">
    {#each labels as label}
      <div class="text-center" style="flex: 1;">
        <span>{label}</span>
      </div>
    {/each}
  </div>
  <div class="text-center mb-2">
    <span class="small text-muted">Intermediate values allowed</span>
  </div>
  <div class="text-center mt-2">
    <span class="small fw-medium text-primary">
      Votre réponse: {value !== null && value !== undefined ? value.toFixed(2) : "-"}
      {#if !hasInteracted}
        <span class="small text-muted">(Sélectionnez une réponse)</span>
      {/if}
    </span>
  </div>
</div>