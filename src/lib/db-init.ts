// src/lib/db-init.ts
import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

// Database file location (in project root)
const DB_PATH = path.resolve(process.cwd(), 'survey-results.sqlite');

// Ensure DB file exists
if (!fs.existsSync(DB_PATH)) {
  fs.closeSync(fs.openSync(DB_PATH, 'w'));
}

const db = new Database(DB_PATH);

// Meta table for schema versioning
db.exec(`
  CREATE TABLE IF NOT EXISTS meta (
    key TEXT PRIMARY KEY,
    value TEXT
  );
`);

// Survey results table
db.exec(`
  CREATE TABLE IF NOT EXISTS survey_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    respondent_id TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    age INTEGER,
    gender TEXT,
    education_level TEXT,
    pre_q1 INTEGER,
    pre_q2 INTEGER,
    pre_q3 INTEGER,
    post_q1 INTEGER,
    post_q2 INTEGER,
    post_q3 INTEGER,
    raw_answers_json TEXT
  );
`);

// Set schema version if not set
const versionRow = db.prepare('SELECT value FROM meta WHERE key = ?').get('schema_version');
if (!versionRow) {
  db.prepare('INSERT INTO meta (key, value) VALUES (?, ?)').run('schema_version', '1');
}

export default db;