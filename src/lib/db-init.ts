// src/lib/db-init.ts
import * as Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

// Database file location (in project root)
const DB_PATH = path.resolve(process.cwd(), 'survey-results.sqlite');

let db: Database.Database;

try {
  // Ensure DB file exists
  if (!fs.existsSync(DB_PATH)) {
    fs.closeSync(fs.openSync(DB_PATH, 'w'));
  }

  db = new (Database as any)(DB_PATH);
} catch (error) {
  console.error('Failed to initialize database:', error);
  throw error;
}

// Meta table for schema versioning
db.exec(`
  CREATE TABLE IF NOT EXISTS meta (
    key TEXT PRIMARY KEY,
    value TEXT
  );
`);

// Survey results table
db.exec(`
  CREATE TABLE IF NOT EXISTS survey_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    respondent_id TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,

    -- Demographics
    age INTEGER,
    gender TEXT,
    education_level TEXT,
    interest_in_topic INTEGER,

    -- Pre-test answers (environment, economics, health)
    pre_env INTEGER,
    pre_econ INTEGER,
    pre_health INTEGER,

    -- Post-test answers (tech regulation questions)
    post_q1 INTEGER,
    post_q2 INTEGER,
    post_q3 INTEGER,

    -- Raw JSON backup of all answers
    raw_answers_json TEXT
  );
`);

// Set schema version if not set
const versionRow = db.prepare('SELECT value FROM meta WHERE key = ?').get('schema_version');
if (!versionRow) {
  db.prepare('INSERT INTO meta (key, value) VALUES (?, ?)').run('schema_version', '1');
}

export default db;
