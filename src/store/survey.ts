// src/store/survey.ts
import { writable } from 'svelte/store';

export interface SurveyData {
  // Demographics
  age: number | null;
  gender: string;
  educationLevel: string;
  interestInTopic: number | null;
  
  // Pre-test answers (environment, economics, health)
  preTest: {
    env: number | null;
    econ: number | null;
    health: number | null;
  };
  
  // Post-test answers (tech regulation questions)
  postTest: {
    q1: number | null;
    q2: number | null;
    q3: number | null;
  };
  
  // Metadata
  respondentId?: string;
  startTime?: Date;
  completedTime?: Date;
}

const initialData: SurveyData = {
  age: null,
  gender: '',
  educationLevel: '',
  interestInTopic: null,
  preTest: {
    env: null,
    econ: null,
    health: null
  },
  postTest: {
    q1: null,
    q2: null,
    q3: null
  },
  startTime: new Date()
};

export const surveyStore = writable<SurveyData>(initialData);

// Helper functions to update specific parts of the survey
export const updateDemographics = (demographics: Partial<Pick<SurveyData, 'age' | 'gender' | 'educationLevel' | 'interestInTopic'>>) => {
  surveyStore.update(data => ({
    ...data,
    ...demographics
  }));
};

export const updatePreTest = (preTestAnswers: Partial<SurveyData['preTest']>) => {
  surveyStore.update(data => ({
    ...data,
    preTest: {
      ...data.preTest,
      ...preTestAnswers
    }
  }));
};

export const updatePostTest = (postTestAnswers: Partial<SurveyData['postTest']>) => {
  surveyStore.update(data => ({
    ...data,
    postTest: {
      ...data.postTest,
      ...postTestAnswers
    }
  }));
};

// Function to submit all survey data
export const submitSurvey = async (): Promise<{ success: boolean; error?: string }> => {
  try {
    let currentData: SurveyData;
    surveyStore.subscribe(data => {
      currentData = data;
    })();

    // Mark completion time
    surveyStore.update(data => ({
      ...data,
      completedTime: new Date()
    }));

    // Prepare data for API
    const apiData = {
      respondent_id: currentData!.respondentId,
      age: currentData!.age,
      gender: currentData!.gender,
      education_level: currentData!.educationLevel,
      interest_in_topic: currentData!.interestInTopic,
      pre_env: currentData!.preTest.env,
      pre_econ: currentData!.preTest.econ,
      pre_health: currentData!.preTest.health,
      post_q1: currentData!.postTest.q1,
      post_q2: currentData!.postTest.q2,
      post_q3: currentData!.postTest.q3,
      start_time: currentData!.startTime,
      completed_time: currentData!.completedTime
    };

    const response = await fetch('/api/survey', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(apiData)
    });

    const result = await response.json();
    
    if (result.success) {
      // Update store with respondent ID if provided
      if (result.respondent_id) {
        surveyStore.update(data => ({
          ...data,
          respondentId: result.respondent_id
        }));
      }
      return { success: true };
    } else {
      return { success: false, error: result.error || 'Unknown error' };
    }
  } catch (error) {
    console.error('Survey submission error:', error);
    return { success: false, error: (error as Error).message };
  }
};

// Function to reset survey data (for testing or new sessions)
export const resetSurvey = () => {
  surveyStore.set({
    ...initialData,
    startTime: new Date()
  });
};
