/* Import Bootstrap CSS */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Custom styles */
:root {
  --study-primary: #3b82f6;
  --study-secondary: #64748b;
  --study-accent: #dbeafe;
  --study-light: #f8fafc;
  --study-dark: #1e293b;
  --study-success: #10b981;
}

body {
  min-height: 100vh;
  background-color: var(--study-light);
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

/* Custom button styles */
.study-btn-primary {
  background-color: var(--study-primary);
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.6em 1.2em;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.25s, transform 0.2s, box-shadow 0.2s;
}

.study-btn-primary:hover {
  background-color: #2563eb;
  color: white;
}

.study-btn-primary:disabled {
  background-color: #94a3b8;
  cursor: not-allowed;
  opacity: 0.7;
}

/* But<PERSON> with completion indicator */
.study-btn-with-indicator {
  position: relative;
  transition: all 0.3s ease;
}

.study-btn-with-indicator.ready {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.study-btn-with-indicator.ready::after {
  content: '✓';
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: var(--study-success);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.completion-status {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.completion-status.incomplete {
  color: #94a3b8;
}

.completion-status.complete {
  color: var(--study-success);
  font-weight: 500;
}

/* Progress bar styles */
.study-progress-bar {
  background-color: #e9ecef;
  border-radius: 0.25rem;
  height: 0.5rem;
}

.study-progress-fill {
  background-color: var(--study-primary);
  height: 0.5rem;
  border-radius: 0.25rem;
}
