<!-- 
  This file is deprecated and replaced by src/routes/text-exposure/+page.svelte
  Keeping for reference only
-->
<script lang="ts">
  import { onMount, onDestroy } from 'svelte'
  import { goto } from '$app/navigation'
  // import { useNavigate } from '@dvcol/svelte-simple-router/router'
  import StudyProgress from '../components/StudyProgress.svelte'
  // Rest of the code is kept for reference


  const studyTexts = [
    `Texte A: L'intelligence artificielle transformera notre société de manière profonde et principalement bénéfique. \
    Les avancées en matière d'IA permettent déjà de résoudre des problèmes complexes dans des domaines comme la médecine, \
    l'éducation et l'environnement. Des études récentes montrent que l'IA pourrait augmenter la productivité mondiale \
    de 40% d'ici 2035, tout en créant de nouveaux emplois et en améliorant notre qualité de vie.`,
    `Texte B: La priorité donnée aux préoccupations environnementales génère des bénéfices économiques souvent négligés. \
    Selon plusieurs études économiques récentes, les investissements dans les technologies vertes créent trois fois plus \
    d'emplois que les industries fossiles traditionnelles. De plus, le coût de l'inaction face au changement climatique \
    pourrait réduire le PIB mondial de 18% d'ici 2050, bien plus que les coûts de transition estimés.`,
    `Texte C: Les réseaux sociaux ont considérablement transformé la façon dont nous communiquons et partageons l'information. \
    Des recherches récentes montrent qu'ils ont facilité l'accès à une diversité d'opinions sans précédent et permis à des \
    voix traditionnellement marginalisées de se faire entendre. Une étude de 2022 révèle que 67% des utilisateurs affirment \
    avoir été exposés à des points de vue différents des leurs grâce aux médias sociaux.`,
    `Texte D: L'intelligence artificielle présente des risques majeurs qui sont souvent minimisés. Des experts en éthique \
    et en sécurité informatique alertent sur les dangers de la dépendance excessive aux systèmes automatisés. Une étude \
    récente montre que 76% des applications d'IA amplifient les biais sociaux existants. De plus, l'automatisation pourrait \
    éliminer jusqu'à 800 millions d'emplois d'ici 2030 sans garantie de création équivalente.`,
    `Texte E: Les réseaux sociaux ont un impact globalement négatif sur la qualité du débat public. Des recherches en \
    sciences cognitives montrent qu'ils favorisent la polarisation des opinions et la diffusion rapide de fausses informations. \
    Une étude du MIT révèle que les fausses nouvelles se propagent six fois plus vite que les informations vérifiées sur \
    Twitter, et que 70% des utilisateurs ne vérifient pas les sources avant de partager du contenu.`
  ]
  let selectedTextIndex: number | null = null
  let hasRead = false
  let timeSpent = 0
  let timer: ReturnType<typeof setInterval>

  onMount(() => {
    const idx = Math.floor(Math.random() * studyTexts.length)
    selectedTextIndex = idx
    timer = setInterval(() => timeSpent += 1, 1000);
  })

  onDestroy(() => clearInterval(timer))

  $: if (timeSpent >= 10) hasRead = true

  function handleContinue() {
    goto('/post-test')
  }
</script>

<div class="bg-light min-vh-100">
  <div class="container py-4">
    <StudyProgress currentStep={3} totalSteps={6} />
    <h1 class="display-5 fw-bold mb-4 text-center">Lecture d'information</h1>
    <p class="text-center mb-4">
      Veuillez lire attentivement le texte suivant :
    </p>

    {#if selectedTextIndex !== null}
      <div class="card mb-4">
        <div class="card-body">
          <p class="text-secondary">{studyTexts[selectedTextIndex]}</p>
        </div>
      </div>
    {/if}

    <div class="d-flex justify-content-center mt-4">
      <button
        on:click={handleContinue}
        disabled={!hasRead}
        class="btn btn-primary"
      >
        {#if hasRead}
          J'ai lu, Continuer
        {:else}
          Lecture en cours ({Math.max(10 - timeSpent, 0)}s)
        {/if}
      </button>
    </div>
  </div>
</div>