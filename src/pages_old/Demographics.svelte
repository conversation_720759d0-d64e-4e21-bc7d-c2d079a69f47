<!-- 
  This file is deprecated and replaced by src/routes/demographics/+page.svelte
  Keeping for reference only
-->
<script lang="ts">
  import { onDestroy } from 'svelte'
  // import { useNavigate } from '@dvcol/svelte-simple-router/router'
  import StudyProgress from '../components/StudyProgress.svelte'
  import LikertScale from '../components/LikertScale.svelte'
  import { goto } from '$app/navigation';

  // const { push } = useNavigate()
  // Rest of the code is kept for reference

let demographics: Record<string, any> = {
  age: null,
  gender: '',
  educationLevel: '',
  interestInTopic: null
};

function handleAge(e: Event) {
  const val = parseInt((e.target as HTMLInputElement).value) || null;
  demographics.age = val;
}

function handleGender(e: Event) {
  const val = (e.target as HTMLSelectElement).value;
  demographics.gender = val;
}

function handleEducation(e: Event) {
  const val = (e.target as HTMLSelectElement).value;
  demographics.educationLevel = val;
}

function handleInterest(val: number) {
  demographics.interestInTopic = val;
}

function isComplete() {
  return (
    demographics.age !== null &&
    demographics.gender &&
    demographics.educationLevel &&
    demographics.interestInTopic !== null
  );
}

function handleSubmit() {
  if (isComplete()) {
    goto('/thank-you');
  } else {
    alert('Veuillez remplir tous les champs avant de soumettre.');
  }
}
</script>

<div class="container-fluid min-vh-100 bg-light py-4">
  <div class="container py-4">
    <StudyProgress currentStep={5} totalSteps={6} />
    <div class="card shadow-sm mx-auto" style="max-width: 540px;">
      <div class="card-body">
        <h1 class="card-title display-5 fw-bold mb-4 text-center">Quelques informations sur vous</h1>
        <p class="card-text text-center mb-4">
          Pour terminer, merci de nous fournir quelques informations démographiques :
        </p>

        <form>
          <div class="mb-3">
            <label for="age" class="form-label">Âge</label>
            <input
              id="age" type="number" min="18" max="120" placeholder="Votre âge"
              value={demographics.age ?? ''}
              on:input={handleAge}
              class="form-control"
            />
          </div>

          <div class="mb-3">
            <label for="gender" class="form-label">Genre</label>
            <select id="gender" bind:value={demographics.gender} on:change={handleGender} class="form-select">
              <option value="" disabled selected>Sélectionner...</option>
              <option>Homme</option>
              <option>Femme</option>
              <option>Non-binaire</option>
              <option>Autre</option>
              <option>Préfère ne pas répondre</option>
            </select>
          </div>

          <div class="mb-3">
            <label for="education" class="form-label">Niveau d'études</label>
            <select id="education" bind:value={demographics.educationLevel} on:change={handleEducation} class="form-select">
              <option value="" disabled selected>Sélectionner...</option>
              <option>Sans diplôme</option>
              <option>Brevet/BEP/CAP</option>
              <option>Bac</option>
              <option>Bac+2</option>
              <option>Licence</option>
              <option>Master</option>
              <option>Doctorat</option>
              <option>Autre</option>
            </select>
          </div>

          <div class="mb-3">
            <label for="interest" class="form-label mb-2">Intérêt pour le sujet de l'étude</label>
            <LikertScale
              question=""
              value={demographics.interestInTopic}
              onChange={handleInterest}
              labels={["Pas du tout intéressé", "Peu intéressé", "Modérément intéressé", "Intéressé", "Très intéressé"]}
            />
          </div>

          <div class="text-center mt-4">
            <button type="button" on:click={handleSubmit} class="btn btn-primary btn-lg w-100" disabled={!isComplete()}>
              Soumettre mes réponses
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>