<!-- 
  This file is deprecated and replaced by src/routes/post-test/+page.svelte
  Keeping for reference only
-->
<script lang="ts">
  import { onDestroy } from 'svelte'
  // import { useNavigate } from '@dvcol/svelte-simple-router/router'
  import StudyProgress from '../components/StudyProgress.svelte'
  import LikertScale from '../components/LikertScale.svelte'
  import { goto } from '$app/navigation'
  // Removed studyStore import (store deleted)

  // const { push } = useNavigate()
  // Rest of the code is kept for reference
  let postTestAnswers: Record<string, number> = {}
  let isComplete = false

  // Removed studyStore and isPostTestComplete subscriptions (store deleted)
  // Removed onDestroy cleanup for subscriptions

  function handleAnswer(question: string, value: number) {
    postTestAnswers = { ...postTestAnswers, [question]: value };
    isComplete =
      Object.keys(postTestAnswers).length === 3 &&
      Object.values(postTestAnswers).every(v => typeof v === 'number');
  }

  function handleContinue() {
    if (isComplete) {
      goto('/demographics');
    } else {
      alert('Veuillez répondre à toutes les questions avant de continuer.');
    }
  }
</script>

<div class="container-fluid bg-light min-vh-100 py-4">
  <div class="container py-4">
    <StudyProgress currentStep={4} totalSteps={6} />
    <h1 class="display-5 mb-4 text-center">Vos opinions après lecture</h1>
    <p class="text-center mb-4">
      Après avoir lu le texte, indiquez à nouveau votre niveau d'accord avec les mêmes affirmations :
    </p>

    <div>
      <LikertScale
        question="L'IA aura un impact majoritairement positif."
        value={postTestAnswers.q1}
        onChange={val => handleAnswer('q1', val)}
      />
      <LikertScale
        question="La protection de l'environnement doit primer sur les coûts économiques."
        value={postTestAnswers.q2}
        onChange={val => handleAnswer('q2', val)}
      />
      <LikertScale
        question="Les réseaux sociaux améliorent le débat public."
        value={postTestAnswers.q3}
        onChange={val => handleAnswer('q3', val)}
      />
    </div>

    <div class="text-center mt-4">
      <button
        on:click={handleContinue}
        class="btn btn-primary btn-lg"
        disabled={!isComplete}
      >
        Continuer
      </button>
      {#if isComplete}
        <div class="alert alert-success mt-3" role="alert">
          Toutes les questions ont été répondues. Vous pouvez continuer.
        </div>
      {:else}
        <div class="alert alert-warning mt-3" role="alert">
          Veuillez répondre à toutes les questions pour continuer.
        </div>
      {/if}
    </div>
  </div>
</div>