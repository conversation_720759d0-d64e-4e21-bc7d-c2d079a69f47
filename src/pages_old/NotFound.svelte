<script lang="ts">
  // SvelteKit handles routing; no need for svelte-routing

  // No dynamic path tracking needed for SvelteKit 404 page
</script>

<div class="min-vh-100 d-flex align-items-center justify-content-center bg-light">
  <div class="text-center">
    <h1 class="display-4 fw-bold mb-3">404</h1>
    <p class="fs-4 text-secondary mb-3">Oops! Page not found</p>
    <a href="/" class="text-primary text-decoration-underline">Return to Home</a>
  </div>
</div>