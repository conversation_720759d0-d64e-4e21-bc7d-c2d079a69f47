<!-- 
  This file is deprecated and replaced by src/routes/consent/+page.svelte
  Keeping for reference only
-->
<script lang="ts">
  // import { useNavigate } from '@dvcol/svelte-simple-router/router'
  import StudyProgress from '../components/StudyProgress.svelte'
  // const { push } = useNavigate()
  let consent = false
  const handleContinue = () => {
    if (consent) window.location.href = '/pre-test';
  }
</script>

<div class="min-vh-100 bg-light py-4 px-3">
  <div class="container py-4">
    <StudyProgress currentStep={1} totalSteps={6} />
    <h1 class="display-5 fw-bold mb-4 text-center">Participation à une étude sur les perceptions sociales</h1>
    <div class="mb-4">
      <p class="mb-4">
        Bienvenue ! Vous êtes invité(e) à participer à une étude du Laboratoire de Recherches Cognitives
        (Université de Paris) sur l'influence de l'information sur les attitudes sociales.
        Durée estimée : 5-7 minutes. Votre anonymat est garanti.
      </p>
      <div class="card card-body mb-4">
        <h2 class="card-title mb-3">Formulaire de consentement</h2>
        <ul class="mb-4 ps-4">
          <li>Les données recueillies sont anonymisées et utilisées uniquement à des fins de recherche.</li>
          <li>Vous pouvez arrêter votre participation à tout moment.</li>
          <li>Il n'y a pas de bonnes ou mauvaises réponses, nous nous intéressons à vos opinions sincères.</li>
          <li>En cliquant sur "Accepter et Commencer", vous confirmez avoir lu et compris ces informations.</li>
        </ul>
        <div class="form-check">
          <input id="consent" type="checkbox" bind:checked={consent} class="form-check-input" />
          <label for="consent" class="form-check-label">
            J'ai lu et j'accepte de participer à cette étude
          </label>
        </div>
      </div>
      <div class="text-center mt-4">
        <button on:click={handleContinue} disabled={!consent} class="btn btn-primary">
          Accepter et Commencer
        </button>
      </div>
    </div>
  </div>
</div>