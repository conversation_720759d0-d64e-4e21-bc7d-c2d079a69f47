<!-- 
  This file is deprecated and replaced by src/routes/pre-test/+page.svelte
  Keeping for reference only
-->
<script lang="ts">
  import { onDestroy } from 'svelte'
  import { goto } from '$app/navigation'
  // import { useNavigate } from '@dvcol/svelte-simple-router/router'
  import StudyProgress from '../components/StudyProgress.svelte'
  import LikertScale from '../components/LikertScale.svelte'
  // Rest of the code is kept for reference
  // Removed invalid useNavigate reference (React-specific, not used in Svelte)

  let preTestAnswers: Record<string, number> = {}
  let isComplete = false

  function handleAnswer(question: string, value: number) {
    console.log('handleAnswer called', question, value);
    preTestAnswers = { ...preTestAnswers, [question]: value };
    isComplete = Object.keys(preTestAnswers).length === 3 &&
      Object.values(preTestAnswers).every(v => typeof v === 'number');
  }

  function handleContinue() {
    if (isComplete) {
      goto('/text-exposure');
    } else {
      alert('Veuillez répondre à toutes les questions avant de continuer.');
    }
  }
</script>

<div class="card min-vh-100 bg-light border-0">
  <div class="card-body">
    <StudyProgress currentStep={2} totalSteps={6} />
    <h1 class="card-title display-5 fw-bold text-center mb-4">Vos opinions actuelles</h1>
    <p class="card-text text-center mb-4">
      Indiquez votre niveau d'accord avec les affirmations suivantes :
    </p>
    <div>
      <LikertScale
        question="L'IA aura un impact majoritairement positif."
        value={preTestAnswers.q1}
        onChange={val => handleAnswer('q1', val)}
      />
      <LikertScale
        question="La protection de l'environnement doit primer sur les coûts économiques."
        value={preTestAnswers.q2}
        onChange={val => handleAnswer('q2', val)}
      />
      <LikertScale
        question="Les réseaux sociaux améliorent le débat public."
        value={preTestAnswers.q3}
        onChange={val => handleAnswer('q3', val)}
      />
    </div>
    <div class="text-center mt-4">
      <button
        on:click={handleContinue}
        class="btn btn-primary px-4"
        disabled={!isComplete}
      >
        Continuer
      </button>
      <div class="mt-2">
        {#if isComplete}
          <div class="alert alert-success py-2 mb-0">
            Toutes les questions ont été répondues. Vous pouvez continuer.
          </div>
        {:else}
          <div class="alert alert-warning py-2 mb-0">
            Veuillez répondre à toutes les questions pour continuer.
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>