// test-api.js - Test the survey API endpoint
const testData = {
  age: 25,
  gender: 'femme',
  education_level: 'master',
  interest_in_topic: 4,
  pre_env: 5,
  pre_econ: 3,
  pre_health: 4,
  post_q1: 4,
  post_q2: 2,
  post_q3: 5
};

async function testAPI() {
  try {
    console.log('🧪 Testing survey API endpoint...');
    console.log('📤 Sending test data:', testData);
    
    const response = await fetch('http://localhost:8080/api/survey', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ API test successful!');
      console.log('📥 Response:', result);
    } else {
      console.log('❌ API test failed!');
      console.log('📥 Error response:', result);
    }
  } catch (error) {
    console.log('❌ API test failed with error:', error.message);
  }
}

testAPI();
